/* eslint-disable */
import Vue from 'vue'
import App from './App.vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/zh-CN'

// 全局引入xlsx和file-saver
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

// 将xlsx和file-saver添加到Vue原型中，方便组件内使用
Vue.prototype.$XLSX = XLSX
Vue.prototype.$saveAs = saveAs

Vue.use(ElementUI, { locale })
Vue.config.productionTip = false

new Vue({
  render: h => h(App),
}).$mount('#app')