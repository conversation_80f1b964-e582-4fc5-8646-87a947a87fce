<!-- eslint-disable -->
<template>
  <el-container style="height: 100vh;">
    <el-header style="background-color: #409EFF; color: white; display: flex; align-items: center; justify-content: space-between;">
      <span>调度运行仿真平台</span>
      <div>
        <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">首页</el-link>
        <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">个人中心</el-link>
        <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">系统配置</el-link>
        <el-link href="#" :underline="false" style="color: white; margin-left: 20px;">退出登录</el-link>
      </div>
    </el-header>
    
    <!-- 主体部分 -->
    <el-container>
      <!-- 左侧菜单 -->
      <el-aside width="200px">
        <el-menu
          :default-active="activeIndex"
          class="el-menu-vertical-demo"
          background-color="#545c64"
          text-color="#fff"
          active-text-color="#ffd04b"
          @select="handleMenuSelect">
          
          <el-submenu index="1">
            <template slot="title">
              <i class="el-icon-document"></i>
              <span>基础数据</span>
            </template>
            <el-submenu index="1-1">
              <template slot="title">资源库</template>
              <el-menu-item-group>
                <template slot="title">数据维护</template>
                <el-menu-item index="1-1-1">节点信息</el-menu-item>
                <el-menu-item index="1-1-2">机组信息</el-menu-item>
              </el-menu-item-group>
            </el-submenu>
          </el-submenu>
          
          <!-- 其他左侧菜单项 -->
          <el-menu-item index="2">
            <i class="el-icon-data-line"></i>
            <span>全景网架</span>
          </el-menu-item>
          <el-menu-item index="3">
            <i class="el-icon-folder"></i>
            <span>模型库</span>
          </el-menu-item>
          <el-menu-item index="4">
            <i class="el-icon-coin"></i>
            <span>资源库</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 右侧主内容区 -->
      <el-container>
        <!-- 运行模拟和结果展示的顶部横向标签页 -->
        <el-header height="50px" style="padding: 0;">
          <el-menu 
            :default-active="topActiveIndex" 
            mode="horizontal" 
            style="border-bottom: none;">
            <el-menu-item index="5">运行模拟</el-menu-item>
            <el-menu-item index="6">结果展示</el-menu-item>
            <el-menu-item index="7">方案对比</el-menu-item>
          </el-menu>
        </el-header>
        
        <!-- 主内容区 -->
        <el-main>
          <node-info v-if="currentComponent === 'NodeInfo'"></node-info>
          <unit-info v-else-if="currentComponent === 'UnitInfo'"></unit-info>
          <div v-else class="empty-content">
            <el-empty description="请从左侧菜单选择功能"></el-empty>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </el-container>
</template>

<script>
import NodeInfo from './components/NodeInfo.vue';
import UnitInfo from './components/UnitInfo.vue';

export default {
  name: 'App',
  components: {
    NodeInfo,
    UnitInfo
  },
  data() {
    return {
      activeIndex: '1-1-1',
      topActiveIndex: '5',
      currentComponent: 'NodeInfo' // 默认显示节点信息组件
    };
  },
  methods: {
    handleMenuSelect(index) {
      this.activeIndex = index;
      
      // 根据菜单选择切换组件
      if (index === '1-1-1') {
        this.currentComponent = 'NodeInfo';
      } else if (index === '1-1-2') {
        this.currentComponent = 'UnitInfo';
      } else {
        this.currentComponent = null;
      }
    }
  }
};
</script>

<style>
body {
  margin: 0;
  padding: 0;
  font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
}

.el-header {
  padding: 0 20px;
  background-color: #409EFF;
  color: white;
  line-height: 60px;
}

.el-aside {
  background-color: #545c64;
  color: white;
}

.el-menu {
  border-right: none;
}

.el-main {
  padding: 0;
  background-color: #f0f2f5;
}

.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style>