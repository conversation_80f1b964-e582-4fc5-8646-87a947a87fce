"""
机组数据转换器
实现从电站数据生成机组数据的功能
"""
import pandas as pd
from typing import List, Dict, Any
from datetime import datetime
from core.excel.reader import ExcelReader
from core.excel.writer import ExcelWriter
from processors.backup_manager import BackupManager
from utils.logger import get_main_logger
from utils.exceptions import ConversionError, ExcelReadError, ExcelWriteError

logger = get_main_logger()

class UnitConverter:
    """机组数据转换器"""
    
    def __init__(self, source_file: str, target_file: str):
        """
        初始化转换器
        
        Args:
            source_file (str): 源文件路径
            target_file (str): 目标文件路径
        """
        self.source_file = source_file
        self.target_file = target_file
        self.backup_manager = BackupManager()
        
        # 转换配置
        self.station_sheet = '电站表'
        self.source_sheet = 'Sheet1'
        self.unit_sheet = '机组表'
        self.characteristic_sheet = '特性表'
        self.cutoff_date = datetime(2025, 6, 12)
        
    def convert_stations_to_units(self) -> bool:
        """
        将电站转换为机组
        主要功能：从电站表提取数据，匹配源文件生成机组记录
        
        Returns:
            bool: 转换是否成功
        """
        try:
            logger.info("开始机组数据转换...")
            
            # 1. 创建备份
            backup_path = self.backup_manager.create_backup(
                self.target_file, 
                "机组数据转换"
            )
            logger.info(f"已创建备份: {backup_path}")
            
            # 2. 读取电站数据（类型=300）
            logger.info("读取电站数据...")
            station_data = self._read_station_data()
            if not station_data:
                logger.warning("没有找到符合条件的电站数据")
                return True
            
            # 3. 读取源文件数据
            logger.info("读取源文件数据...")
            source_data = self._read_source_data()
            if source_data is None or source_data.empty:
                raise ConversionError("读取源数据失败")
            
            # 4. 读取特性表数据
            logger.info("读取特性表数据...")
            characteristic_data = self._read_characteristic_data()
            if characteristic_data is None or characteristic_data.empty:
                raise ConversionError("读取特性表数据失败")
            
            # 5. 处理数据转换
            logger.info("处理数据转换...")
            unit_data = self._process_unit_conversion(
                station_data, source_data, characteristic_data
            )
            
            if not unit_data:
                logger.warning("没有生成机组数据")
                return True
            
            # 6. 写入目标文件
            logger.info("写入目标文件...")
            success = self._write_unit_data(unit_data)
            
            if success:
                logger.info(f"✓ 机组数据转换完成，共处理 {len(unit_data)} 条记录")
                return True
            else:
                raise ConversionError("写入目标文件失败")
                
        except Exception as e:
            logger.error(f"机组数据转换失败: {str(e)}")
            return False
    
    def _read_station_data(self) -> List[Dict[str, Any]]:
        """
        读取电站数据（类型=300）
        
        Returns:
            List[Dict[str, Any]]: 电站数据
        """
        try:
            reader = ExcelReader(self.target_file)
            
            if not reader.load_workbook():
                raise ExcelReadError("无法加载目标文件工作簿", self.target_file)
            
            # 读取电站表数据
            data_list = reader.read_sheet_data(self.station_sheet)
            reader.close()

            if not data_list:
                logger.warning("电站表中没有数据")
                return []
            
            # 转换为DataFrame并筛选
            df = pd.DataFrame(data_list)
            
            # 筛选类型为300的电站
            if '类型' in df.columns:
                filtered_df = df[df['类型'] == 300]
                logger.info(f"找到 {len(filtered_df)} 个类型为300的电站")
                return filtered_df.to_dict('records')
            else:
                logger.error("电站表中没有找到'类型'列")
                return []
            
        except Exception as e:
            raise ExcelReadError(f"读取电站数据失败: {str(e)}", self.target_file, self.station_sheet)
    
    def _read_source_data(self) -> pd.DataFrame:
        """
        读取源文件数据
        
        Returns:
            pd.DataFrame: 源数据
        """
        try:
            reader = ExcelReader(self.source_file)
            
            if not reader.load_workbook():
                raise ExcelReadError("无法加载源文件工作簿", self.source_file)
            
            # 读取Sheet1的数据
            data_list = reader.read_sheet_data(self.source_sheet)
            reader.close()

            if not data_list:
                raise ExcelReadError("源文件中没有数据", self.source_file, self.source_sheet)
            
            # 转换为DataFrame
            df = pd.DataFrame(data_list)
            logger.info(f"成功读取源数据，共 {len(df)} 行")
            
            return df
            
        except Exception as e:
            raise ExcelReadError(f"读取源文件失败: {str(e)}", self.source_file, self.source_sheet)
    
    def _read_characteristic_data(self) -> pd.DataFrame:
        """
        读取特性表数据
        
        Returns:
            pd.DataFrame: 特性数据
        """
        try:
            reader = ExcelReader(self.target_file)
            
            if not reader.load_workbook():
                raise ExcelReadError("无法加载目标文件工作簿", self.target_file)
            
            # 读取特性表数据
            data_list = reader.read_sheet_data(self.characteristic_sheet)
            reader.close()

            if not data_list:
                raise ExcelReadError("特性表中没有数据", self.target_file, self.characteristic_sheet)
            
            # 转换为DataFrame
            df = pd.DataFrame(data_list)
            logger.info(f"成功读取特性数据，共 {len(df)} 行")
            
            return df
            
        except Exception as e:
            raise ExcelReadError(f"读取特性表失败: {str(e)}", self.target_file, self.characteristic_sheet)
    
    def _extract_project_name_from_station(self, station_name: str) -> str:
        """
        从电站名称中提取项目名称
        例如：MD_1_博贺电厂二期 -> 博贺电厂二期
        
        Args:
            station_name (str): 电站名称
            
        Returns:
            str: 项目名称
        """
        if not station_name:
            return ""
        
        # 分割电站名称，格式：MD_序号_项目名称
        parts = station_name.split('_', 2)
        if len(parts) >= 3:
            return parts[2]  # 返回项目名称部分
        else:
            logger.warning(f"电站名称格式不正确: {station_name}")
            return station_name
    
    def _parse_production_date(self, date_str: str) -> str:
        """
        解析投产时间
        
        Args:
            date_str (str): 投产时间字符串
            
        Returns:
            str: 格式化的投产年月（YYYYMM）或 "0"
        """
        if not date_str:
            return "0"
        
        try:
            # 尝试解析日期
            if isinstance(date_str, str):
                # 处理常见的日期格式
                for fmt in ['%Y/%m/%d', '%Y-%m-%d', '%Y/%m', '%Y-%m']:
                    try:
                        date_obj = datetime.strptime(date_str, fmt)
                        break
                    except ValueError:
                        continue
                else:
                    logger.warning(f"无法解析投产时间: {date_str}")
                    return "0"
            else:
                # 如果是datetime对象
                date_obj = date_str
            
            # 检查是否早于截止日期
            if date_obj < self.cutoff_date:
                return "0"
            else:
                return date_obj.strftime("%Y%m")
                
        except Exception as e:
            logger.warning(f"解析投产时间失败: {date_str}, 错误: {str(e)}")
            return "0"
