<!-- eslint-disable -->
<template>
  <el-container style="height: 100%;">
    <!-- 左侧树形导航 -->
    <el-aside width="250px" style="border-right: 1px solid #e6e6e6; padding: 10px;">
      <div class="tree-header">
        <span>电站及机组</span>
      </div>
      <el-tree
        :data="treeData"
        :props="defaultProps"
        :highlight-current="true"
        @node-click="handleNodeClick"
        default-expand-all
        node-key="id"
        :expand-on-click-node="false"
        ref="unitTree"
      >
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <i :class="data.type === 'station' ? 'el-icon-office-building' : 'el-icon-cpu'" style="margin-right: 5px;"></i>
          <span>{{ node.label }}</span>
        </span>
      </el-tree>
    </el-aside>

    <!-- 主内容区 -->
    <el-main style="padding: 0;">
      <div v-if="!currentUnit">
        <el-empty description="请从左侧选择一个机组"></el-empty>
      </div>
      <div v-else>
        <!-- 机组基本信息表单 -->
        <el-card class="box-card" style="margin-bottom: 15px;">
          <div slot="header" class="clearfix">
            <span>机组基本信息</span>
          </div>
          <el-form :model="unitForm" ref="unitForm" label-width="120px" :rules="unitRules" size="small">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="机组ID" prop="id">
                  <el-input v-model="unitForm.id" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="机组名称" prop="name">
                  <el-input v-model="unitForm.name"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属电站" prop="stationId">
                  <el-select v-model="unitForm.stationId" placeholder="请选择电站">
                    <el-option
                      v-for="station in stationOptions"
                      :key="station.id"
                      :label="station.name"
                      :value="station.id">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="机组类型" prop="type">
                  <el-select v-model="unitForm.type" placeholder="请选择类型">
                    <el-option label="水轮发电机组" value="hydro"></el-option>
                    <el-option label="抽水蓄能机组" value="pumped_storage"></el-option>
                    <el-option label="其他" value="other"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="额定功率(MW)" prop="ratedPower">
                  <el-input-number v-model="unitForm.ratedPower" :min="0" :precision="2" :step="0.1"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="最大出力(MW)" prop="maxOutput">
                  <el-input-number v-model="unitForm.maxOutput" :min="0" :precision="2" :step="0.1"></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="最小出力(MW)" prop="minOutput">
                  <el-input-number v-model="unitForm.minOutput" :min="0" :precision="2" :step="0.1"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="投产日期" prop="startDate">
                  <el-date-picker
                    v-model="unitForm.startDate"
                    type="date"
                    placeholder="选择日期"
                    format="yyyyMMdd"
                    value-format="yyyyMMdd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="退役日期" prop="endDate">
                  <el-date-picker
                    v-model="unitForm.endDate"
                    type="date"
                    placeholder="选择日期"
                    format="yyyyMMdd"
                    value-format="yyyyMMdd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item>
              <el-button type="primary" @click="saveUnitInfo">保存</el-button>
              <el-button @click="resetUnitForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 三段式曲线配置 -->
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>三段式曲线配置</span>
          </div>
          <div class="curve-toolbar">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="addCurvePoint">添加段</el-button>
            <el-button size="small" type="danger" icon="el-icon-delete" @click="deleteCurvePoint" :disabled="!selectedCurvePoints.length">删除段</el-button>
            <el-button size="small" type="primary" icon="el-icon-upload2" @click="importCurveData">导入</el-button>
            <el-button size="small" type="success" icon="el-icon-download" @click="exportCurveData">导出</el-button>
            <el-button size="small" type="primary" @click="saveCurveData">保存</el-button>
            <input
              ref="curveDataUpload"
              type="file"
              accept=".xlsx, .xls"
              style="display: none;"
              @change="handleCurveImport"
            />
          </div>
          <el-table
            :data="curveData"
            border
            style="width: 100%"
            @selection-change="handleCurveSelectionChange"
            height="300"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column label="段序号" prop="sequence" width="80">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.sequence" :min="1" :max="999" size="mini" controls-position="right"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="流量(m³/s)" prop="flow" width="120">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.flow" :min="0" :precision="2" size="mini" controls-position="right"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="水头(m)" prop="waterHead" width="120">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.waterHead" :min="0" :precision="2" size="mini" controls-position="right"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="出力(MW)" prop="output" width="120">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.output" :min="0" :precision="2" size="mini" controls-position="right"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="效率(%)" prop="efficiency">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.efficiency" :min="0" :max="100" :precision="2" size="mini" controls-position="right"></el-input-number>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </el-main>
  </el-container>
</template>

<script>
// 初始化电站和机组数据
const initialStations = [
  {
    id: 'S001',
    name: '三峡水电站',
    type: 'station'
  },
  {
    id: 'S002',
    name: '葛洲坝水电站',
    type: 'station'
  },
  {
    id: 'S003',
    name: '溪洛渡水电站',
    type: 'station'
  }
];

const initialUnits = [
  {
    id: 'U001',
    name: '三峡1号机组',
    stationId: 'S001',
    type: 'hydro',
    ratedPower: 700,
    maxOutput: 710,
    minOutput: 350,
    startDate: '20030801',
    endDate: '21000101',
    parentId: 'S001',
    isLeaf: true
  },
  {
    id: 'U002',
    name: '三峡2号机组',
    stationId: 'S001',
    type: 'hydro',
    ratedPower: 700,
    maxOutput: 710,
    minOutput: 350,
    startDate: '20030801',
    endDate: '21000101',
    parentId: 'S001',
    isLeaf: true
  },
  {
    id: 'U003',
    name: '葛洲坝1号机组',
    stationId: 'S002',
    type: 'hydro',
    ratedPower: 125,
    maxOutput: 130,
    minOutput: 60,
    startDate: '19810901',
    endDate: '21000101',
    parentId: 'S002',
    isLeaf: true
  },
  {
    id: 'U004',
    name: '溪洛渡1号机组',
    stationId: 'S003',
    type: 'hydro',
    ratedPower: 770,
    maxOutput: 780,
    minOutput: 380,
    startDate: '20130701',
    endDate: '21000101',
    parentId: 'S003',
    isLeaf: true
  }
];

// 初始化三段式曲线数据
const initialCurveData = {
  'U001': [
    { sequence: 1, flow: 100, waterHead: 80, output: 200, efficiency: 85 },
    { sequence: 2, flow: 200, waterHead: 85, output: 420, efficiency: 90 },
    { sequence: 3, flow: 300, waterHead: 90, output: 650, efficiency: 93 },
    { sequence: 4, flow: 350, waterHead: 95, output: 710, efficiency: 91 }
  ],
  'U002': [
    { sequence: 1, flow: 100, waterHead: 80, output: 200, efficiency: 85 },
    { sequence: 2, flow: 200, waterHead: 85, output: 420, efficiency: 90 },
    { sequence: 3, flow: 300, waterHead: 90, output: 650, efficiency: 93 },
    { sequence: 4, flow: 350, waterHead: 95, output: 710, efficiency: 91 }
  ],
  'U003': [
    { sequence: 1, flow: 50, waterHead: 40, output: 30, efficiency: 80 },
    { sequence: 2, flow: 100, waterHead: 42, output: 65, efficiency: 85 },
    { sequence: 3, flow: 150, waterHead: 45, output: 100, efficiency: 88 },
    { sequence: 4, flow: 180, waterHead: 47, output: 130, efficiency: 86 }
  ],
  'U004': [
    { sequence: 1, flow: 120, waterHead: 200, output: 250, efficiency: 87 },
    { sequence: 2, flow: 240, waterHead: 205, output: 500, efficiency: 92 },
    { sequence: 3, flow: 360, waterHead: 210, output: 780, efficiency: 94 }
  ]
};

// 从localStorage获取数据，如果没有则使用初始数据
let stations = [];
let units = [];
let curveData = {};

try {
  const savedStations = localStorage.getItem('hydroStationsData');
  const savedUnits = localStorage.getItem('hydroUnitsData');
  const savedCurveData = localStorage.getItem('hydroCurveData');
  
  stations = savedStations ? JSON.parse(savedStations) : [...initialStations];
  units = savedUnits ? JSON.parse(savedUnits) : [...initialUnits];
  curveData = savedCurveData ? JSON.parse(savedCurveData) : {...initialCurveData};
} catch (e) {
  console.error('Error loading data from localStorage:', e);
  stations = [...initialStations];
  units = [...initialUnits];
  curveData = {...initialCurveData};
}

// 保存数据到localStorage的辅助函数
function saveStationsToStorage() {
  try {
    localStorage.setItem('hydroStationsData', JSON.stringify(stations));
  } catch (e) {
    console.error('Error saving stations data to localStorage:', e);
  }
}

function saveUnitsToStorage() {
  try {
    localStorage.setItem('hydroUnitsData', JSON.stringify(units));
  } catch (e) {
    console.error('Error saving units data to localStorage:', e);
  }
}

function saveCurveDataToStorage() {
  try {
    localStorage.setItem('hydroCurveData', JSON.stringify(curveData));
  } catch (e) {
    console.error('Error saving curve data to localStorage:', e);
  }
}

export default {
  name: 'UnitInfo',
  data() {
    return {
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        isLeaf: 'isLeaf'
      },
      currentUnit: null,
      unitForm: {
        id: '',
        name: '',
        stationId: '',
        type: '',
        ratedPower: 0,
        maxOutput: 0,
        minOutput: 0,
        startDate: '',
        endDate: ''
      },
      unitRules: {
        name: [
          { required: true, message: '请输入机组名称', trigger: 'blur' }
        ],
        stationId: [
          { required: true, message: '请选择所属电站', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择机组类型', trigger: 'change' }
        ],
        ratedPower: [
          { required: true, message: '请输入额定功率', trigger: 'blur' },
          { type: 'number', min: 0, message: '额定功率必须大于0', trigger: 'blur' }
        ],
        maxOutput: [
          { required: true, message: '请输入最大出力', trigger: 'blur' },
          { type: 'number', min: 0, message: '最大出力必须大于0', trigger: 'blur' }
        ],
        minOutput: [
          { required: true, message: '请输入最小出力', trigger: 'blur' },
          { type: 'number', min: 0, message: '最小出力必须大于0', trigger: 'blur' }
        ],
        startDate: [
          { required: true, message: '请选择投产日期', trigger: 'change' }
        ],
        endDate: [
          { required: true, message: '请选择退役日期', trigger: 'change' }
        ]
      },
      stationOptions: [],
      curveData: [],
      selectedCurvePoints: []
    };
  },
  created() {
    this.initTreeData();
    this.stationOptions = stations.map(station => ({
      id: station.id,
      name: station.name
    }));
  },
  methods: {
    // 初始化树形结构数据
    initTreeData() {
      // 构建树形结构
      this.treeData = stations.map(station => {
        const stationNode = { ...station };
        stationNode.children = units.filter(unit => unit.stationId === station.id)
          .map(unit => ({
            id: unit.id,
            name: unit.name,
            type: 'unit',
            isLeaf: true,
            parentId: station.id
          }));
        return stationNode;
      });
    },
    
    // 处理树节点点击
    handleNodeClick(data) {
      if (data.type === 'unit') {
        // 加载机组数据
        const unit = units.find(u => u.id === data.id);
        if (unit) {
          this.currentUnit = unit;
          this.unitForm = { ...unit };
          
          // 加载三段式曲线数据
          this.curveData = curveData[unit.id] ? [...curveData[unit.id]] : [];
        }
      } else {
        // 如果点击的是电站，不加载机组数据
        this.currentUnit = null;
        this.curveData = [];
      }
    },
    
    // 保存机组基本信息
    saveUnitInfo() {
      this.$refs.unitForm.validate((valid) => {
        if (valid) {
          // 验证最小出力小于最大出力
          if (this.unitForm.minOutput >= this.unitForm.maxOutput) {
            this.$message.error('最小出力必须小于最大出力');
            return;
          }
          
          // 验证投产日期早于退役日期
          if (this.unitForm.startDate >= this.unitForm.endDate) {
            this.$message.error('投产日期必须早于退役日期');
            return;
          }
          
          // 更新机组数据
          const index = units.findIndex(u => u.id === this.unitForm.id);
          if (index !== -1) {
            units[index] = { ...this.unitForm };
            
            // 如果电站发生变化，需要更新树形结构
            if (units[index].stationId !== this.currentUnit.stationId) {
              this.currentUnit = { ...units[index] };
              this.initTreeData();
            } else {
              this.currentUnit = { ...units[index] };
            }
            
            // 保存到localStorage
            saveUnitsToStorage();
            
            this.$message.success('机组信息保存成功');
          }
        } else {
          this.$message.error('表单验证失败，请检查输入');
        }
      });
    },
    
    // 重置机组表单
    resetUnitForm() {
      if (this.currentUnit) {
        this.unitForm = { ...this.currentUnit };
      }
    },
    
    // 添加曲线点
    addCurvePoint() {
      if (!this.currentUnit) {
        this.$message.warning('请先选择一个机组');
        return;
      }
      
      // 计算新的序号
      const maxSequence = this.curveData.length > 0 
        ? Math.max(...this.curveData.map(item => item.sequence))
        : 0;
      
      this.curveData.push({
        sequence: maxSequence + 1,
        flow: 0,
        waterHead: 0,
        output: 0,
        efficiency: 0
      });
    },
    
    // 删除曲线点
    deleteCurvePoint() {
      if (this.selectedCurvePoints.length === 0) {
        this.$message.warning('请先选择要删除的数据点');
        return;
      }
      
      const selectedIds = this.selectedCurvePoints.map(item => item.sequence);
      this.curveData = this.curveData.filter(item => !selectedIds.includes(item.sequence));
      
      // 重新排序
      this.curveData = this.curveData.map((item, index) => ({
        ...item,
        sequence: index + 1
      }));
      
      this.selectedCurvePoints = [];
    },
    
    // 处理曲线数据选择变化
    handleCurveSelectionChange(selection) {
      this.selectedCurvePoints = selection;
    },
    
    // 保存曲线数据
    saveCurveData() {
      if (!this.currentUnit) {
        this.$message.warning('请先选择一个机组');
        return;
      }
      
      // 验证数据
      const hasInvalidData = this.curveData.some(item => 
        item.flow <= 0 || 
        item.waterHead <= 0 || 
        item.output < 0 || 
        (item.efficiency < 0 || item.efficiency > 100)
      );
      
      if (hasInvalidData) {
        this.$message.error('存在无效数据，请检查输入');
        return;
      }
      
      // 排序
      this.curveData.sort((a, b) => a.sequence - b.sequence);
      
      // 保存数据
      curveData[this.currentUnit.id] = [...this.curveData];
      saveCurveDataToStorage();
      
      this.$message.success('三段式曲线数据保存成功');
    },
    
    // 导入曲线数据
    importCurveData() {
      if (!this.currentUnit) {
        this.$message.warning('请先选择一个机组');
        return;
      }
      
      this.$refs.curveDataUpload.click();
    },
    
    // 处理曲线数据导入
    handleCurveImport(e) {
      const files = e.target.files;
      if (!files || !files.length) {
        return;
      }
      
      const file = files[0];
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = e.target.result;
          const workbook = this.$XLSX.read(data, { type: 'array' });
          
          // 默认读取第一个工作表
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // 转换为JSON格式
          const jsonData = this.$XLSX.utils.sheet_to_json(worksheet);
          
          if (jsonData && jsonData.length > 0) {
            // 处理导入的数据
            this.processCurveImport(jsonData);
          } else {
            this.$message.error('导入的Excel文件未包含有效数据！');
          }
        } catch (error) {
          console.error('Excel导入错误:', error);
          this.$message.error('Excel导入失败，请检查文件格式！');
        }
        
        // 清空文件输入，以便于下次导入同一个文件
        this.$refs.curveDataUpload.value = '';
      };
      
      reader.readAsArrayBuffer(file);
    },
    
    // 处理曲线数据导入
    processCurveImport(data) {
      try {
        const importedPoints = data.map((item, index) => {
          return {
            sequence: item['段序号'] || index + 1,
            flow: parseFloat(item['流量(m³/s)']) || 0,
            waterHead: parseFloat(item['水头(m)']) || 0,
            output: parseFloat(item['出力(MW)']) || 0,
            efficiency: parseFloat(item['效率(%)']) || 0
          };
        });
        
        // 验证数据
        const hasInvalidData = importedPoints.some(item => 
          item.flow <= 0 || 
          item.waterHead <= 0 || 
          item.output < 0 || 
          (item.efficiency < 0 || item.efficiency > 100)
        );
        
        if (hasInvalidData) {
          this.$message.warning('导入的数据中存在无效值，已设为默认值');
        }
        
        // 更新曲线数据
        this.curveData = importedPoints;
        
        this.$message.success(`成功导入 ${importedPoints.length} 条曲线数据`);
      } catch (error) {
        console.error('处理导入数据错误:', error);
        this.$message.error('导入数据处理失败！');
      }
    },
    
    // 导出曲线数据
    exportCurveData() {
      if (!this.currentUnit) {
        this.$message.warning('请先选择一个机组');
        return;
      }
      
      if (this.curveData.length === 0) {
        this.$message.warning('没有可导出的曲线数据');
        return;
      }
      
      // 定义表头
      const header = {
        sequence: '段序号',
        flow: '流量(m³/s)',
        waterHead: '水头(m)',
        output: '出力(MW)',
        efficiency: '效率(%)'
      };
      
      // 准备导出数据
      const data = this.curveData.map(item => {
        let row = {};
        for (const key in header) {
          row[header[key]] = item[key];
        }
        return row;
      });
      
      // 创建工作表
      const worksheet = this.$XLSX.utils.json_to_sheet(data);
      const workbook = this.$XLSX.utils.book_new();
      this.$XLSX.utils.book_append_sheet(workbook, worksheet, '三段式曲线');
      
      // 设置列宽
      const colWidths = Object.keys(header).map(key => {
        return { wch: Math.max(header[key].length * 2, 10) };
      });
      worksheet['!cols'] = colWidths;
      
      // 导出文件
      const excelBuffer = this.$XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });
      this.$saveAs(blob, `${this.currentUnit.name}_三段式曲线.xlsx`);
      
      this.$message.success('导出成功');
    }
  }
};
</script>

<style scoped>
.tree-header {
  font-weight: bold;
  padding: 0 0 10px 0;
  border-bottom: 1px solid #e6e6e6;
  margin-bottom: 10px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.box-card {
  margin: 10px;
}

.curve-toolbar {
  margin-bottom: 15px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}
</style> 