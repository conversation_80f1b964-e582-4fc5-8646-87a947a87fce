# Python virtual environments
.venv/
venv/
env/
__pycache__/
HUST/backups/
backups/
*.py[cod]
*$py.class

# IDE files
.idea/
.vscode/

# Large Excel files (optional, uncomment if you don't want to track these)
# *.xlsx
# *.xls
01 电源明细表-模板 - 副本.xlsx
01 电源明细表-模板.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906.xls
240923-GOPT_GD_YX-V245-十五五2030-20240906.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本.xls
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本.xlsx
~$240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本 - 副本.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本 - 副本.xlsx
删除机组报价.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本 - 副本.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本 - 副本.xlsx
删除机组报价 - 副本.xlsx
HUST/电源明细表手动转HUST.xlsx
HUST/backups/电源明细表手动转HUST_backup_20250611_152515_电站名称转换.xlsx
HUST/~$电源明细表手动转HUST.xlsx
