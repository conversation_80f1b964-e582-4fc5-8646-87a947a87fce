<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试页面</title>
  <!-- 引入Vue 2 -->
  <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
  <!-- 引入Element UI样式 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/theme-chalk/index.css">
  <!-- 引入Element UI组件库 -->
  <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/index.js"></script>
  <!-- 引入中文语言包 -->
  <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/umd/locale/zh-CN.js"></script>
</head>
<body>
  <div id="app">
    <el-container style="height: 100vh;">
      <el-header style="background-color: #409EFF; color: white;">
        调度运行仿真平台
      </el-header>
      <el-main>
        <el-button type="primary" @click="showMessage">测试按钮</el-button>
      </el-main>
    </el-container>
  </div>

  <script>
    // 设置Element UI语言为中文
    ELEMENT.locale(ELEMENT.lang.zhCN);

    new Vue({
      el: '#app',
      data: {
        message: 'Hello Vue!'
      },
      methods: {
        showMessage() {
          this.$message.success('Element UI 和 Vue 2 运行正常！');
        }
      }
    });
  </script>
</body>
</html> 